:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Light theme variables */
:root[data-theme="light"] {
  --bg-color: #ffffff;
  --text-color: #213547;
  --text-secondary: #666666;
  --border-color: #e0e0e0;
  --accent-color: #646cff;
  --accent-hover: #535bf2;
  --button-bg: #f9f9f9;
  --button-hover-bg: #e9e9e9;
  --card-bg: #ffffff;
  --header-bg: rgba(255, 255, 255, 0.8);
  --shadow: rgba(0, 0, 0, 0.1);
}

/* Dark theme variables */
:root[data-theme="dark"] {
  --bg-color: #242424;
  --text-color: rgba(255, 255, 255, 0.87);
  --text-secondary: #888888;
  --border-color: #404040;
  --accent-color: #646cff;
  --accent-hover: #535bf2;
  --button-bg: #1a1a1a;
  --button-hover-bg: #2a2a2a;
  --card-bg: #2a2a2a;
  --header-bg: rgba(36, 36, 36, 0.8);
  --shadow: rgba(0, 0, 0, 0.3);
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

a {
  font-weight: 500;
  color: var(--accent-color);
  text-decoration: inherit;
  transition: color 0.3s ease;
}
a:hover {
  color: var(--accent-hover);
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
  color: var(--text-color);
}

h2, h3, h4, h5, h6 {
  color: var(--text-color);
}

p {
  color: var(--text-secondary);
}

button {
  border-radius: 8px;
  border: 1px solid var(--border-color);
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--button-bg);
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
}
button:hover {
  border-color: var(--accent-color);
  background-color: var(--button-hover-bg);
}
button:focus,
button:focus-visible {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}


