import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { ThemeToggle } from './ThemeToggle';
import './Header.css';

export const Header: React.FC = () => {
  const { user, logout, isAuthenticated } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="header">
      <div className="header-content">
        <h1 className="header-title">Stagecoach App</h1>
        <div className="header-actions">
          {isAuthenticated && user && (
            <div className="user-info">
              <span className="welcome-text">Welcome, {user.firstName}!</span>
              <button onClick={handleLogout} className="logout-button">
                Sign Out
              </button>
            </div>
          )}
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
};
