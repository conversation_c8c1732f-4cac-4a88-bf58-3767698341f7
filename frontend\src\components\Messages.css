.messages-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  background: var(--bg-color);
  min-height: 100vh;
}

.messages-header {
  text-align: center;
  margin-bottom: 2rem;
}

.messages-header h2 {
  color: var(--text-color);
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.messages-header p {
  color: var(--text-secondary, #666);
  font-size: 1rem;
  margin: 0;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: var(--text-color);
  font-size: 1.1rem;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border: 1px solid #fcc;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.retry-button {
  background: #c33;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
}

.retry-button:hover {
  background: #a22;
}

.message-form {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.message-input {
  width: 100%;
  padding: 1rem;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 8px;
  font-size: 1rem;
  font-family: inherit;
  background: var(--input-bg, var(--bg-color));
  color: var(--text-color);
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.1);
}

.message-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.character-count {
  color: var(--text-secondary, #666);
  font-size: 0.875rem;
}

.submit-button {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-button:hover:not(:disabled) {
  background: var(--accent-hover, #5a5fcf);
  transform: translateY(-1px);
}

.submit-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.no-messages {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary, #666);
  background: var(--card-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
}

.message-item {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.message-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.user-name {
  color: var(--text-color);
  font-weight: 600;
  font-size: 1rem;
}

.user-email {
  color: var(--text-secondary, #666);
  font-size: 0.875rem;
}

.message-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.message-time {
  color: var(--text-secondary, #666);
  font-size: 0.875rem;
}

.owner-badge {
  background: var(--accent-color);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.message-content {
  color: var(--text-color);
  font-size: 1rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.messages-footer {
  text-align: center;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
}

.messages-footer p {
  color: var(--text-secondary, #666);
  font-size: 0.875rem;
  margin: 0;
}

/* Dark mode specific styles */
[data-theme="dark"] .messages-container {
  --card-bg: #2a2a2a;
  --border-color: rgba(255, 255, 255, 0.1);
  --input-bg: #1a1a1a;
  --text-secondary: #aaa;
}

/* Light mode specific styles */
[data-theme="light"] .messages-container {
  --card-bg: #ffffff;
  --border-color: #ddd;
  --input-bg: #ffffff;
  --text-secondary: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .messages-container {
    padding: 1rem;
  }
  
  .messages-header h2 {
    font-size: 1.5rem;
  }
  
  .message-form {
    padding: 1rem;
  }
  
  .message-item {
    padding: 1rem;
  }
  
  .message-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .message-meta {
    flex-direction: row;
    align-items: center;
    gap: 0.75rem;
  }
  
  .form-footer {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }
  
  .submit-button {
    width: 100%;
  }
}
