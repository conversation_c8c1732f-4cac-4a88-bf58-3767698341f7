# .NET 8 Backend REST API with JWT Authentication

This is a .NET 8 Web API backend with Entity Framework Core, SQLite database, and complete JWT authentication system.

## Features

- ✅ **JWT Authentication** with Bearer tokens
- ✅ **User Registration & Login** with secure password hashing
- ✅ **Protected Endpoints** with authorization middleware
- ✅ **User Profile Management** with update capabilities
- ✅ **Password Change** functionality
- ✅ **Entity Framework Core** with SQLite database
- ✅ **Database Migrations** for schema management
- ✅ **CORS Configuration** for React frontend integration
- ✅ **Swagger/OpenAPI** documentation with authentication
- ✅ **JSON Serialization** with cycle handling
- ✅ **Comprehensive Logging** and error handling
- ✅ **BCrypt Password Hashing** for security

## Authentication System

The backend includes a complete authentication system:
- **User Entity**: User model with profile information
- **JWT Service**: Token generation and validation
- **Auth Controller**: Registration, login, and profile management
- **Password Security**: BCrypt hashing for secure password storage
- **Protected Routes**: Authorization middleware for secure endpoints

## Database Schema

### User Entity
- **Id**: Primary key (auto-increment)
- **FirstName**: User's first name (required, max 100 chars)
- **LastName**: User's last name (required, max 100 chars)
- **Email**: User's email address (required, unique, max 255 chars)
- **PasswordHash**: BCrypt hashed password (required)
- **IsActive**: Account status (boolean, default true)
- **CreatedAt**: Account creation timestamp
- **UpdatedAt**: Last update timestamp
- **LastLoginAt**: Last login timestamp (nullable)

### Message Entity
- **Id**: Primary key (auto-increment)
- **Content**: Message content (required, max 1000 chars)
- **CreatedAt**: Message creation timestamp
- **UpdatedAt**: Last update timestamp
- **UserId**: Foreign key to User (required)
- **User**: Navigation property to User entity

## Getting Started

### Prerequisites
- .NET 8 SDK
- SQLite (included with .NET)

### Running the API

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Restore packages:**
   ```bash
   dotnet restore
   ```

3. **Run the application:**
   ```bash
   dotnet run
   ```

4. **Access the API:**
   - API Base URL: `http://localhost:5073`
   - Swagger UI: `http://localhost:5073/swagger`

### Database

The SQLite database is automatically created with the Users table when the application starts. The database includes:
- **Users table**: Complete user authentication schema
- **Unique email constraint**: Prevents duplicate email registrations
- **Automatic timestamps**: CreatedAt and UpdatedAt with default values

### CORS Configuration

The API is configured to accept requests from:
- `http://localhost:5173` (Vite dev server - primary)
- `http://localhost:5174` (Vite dev server - fallback)
- `http://localhost:3000` (Create React App)

## API Endpoints

### Authentication Endpoints

#### POST /api/Auth/register
Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires": "2025-06-20T07:59:19.230Z",
  "user": {
    "id": 1,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "isActive": true,
    "createdAt": "2025-06-20T06:59:19.003Z",
    "lastLoginAt": null,
    "fullName": "John Doe"
  }
}
```

#### POST /api/Auth/login
Authenticate user and get JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:** Same as registration response with updated lastLoginAt.

#### GET /api/Auth/me
Get current user profile (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "id": 1,
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "isActive": true,
  "createdAt": "2025-06-20T06:59:19.003Z",
  "lastLoginAt": "2025-06-20T06:59:31.925Z",
  "fullName": "John Doe"
}
```

#### PUT /api/Auth/profile
Update user profile (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Smith",
  "email": "<EMAIL>"
}
```

#### PUT /api/Auth/change-password
Change user password (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123",
  "confirmNewPassword": "newpassword123"
}
```

### Messages Endpoints

#### GET /api/Messages
Get all messages from all users (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Items per page (default: 50, max: 100)

**Response:**
```json
{
  "messages": [
    {
      "id": 2,
      "content": "This is my second message! The weather is great today. ☀️",
      "createdAt": "2025-06-20T07:10:53.724Z",
      "updatedAt": "2025-06-20T07:10:53.724Z",
      "userId": 1,
      "user": {
        "id": 1,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "fullName": "John Doe"
      }
    }
  ],
  "totalCount": 2,
  "page": 1,
  "pageSize": 50,
  "totalPages": 1
}
```

#### POST /api/Messages
Create a new message (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "content": "Hello everyone! This is my first message. 👋"
}
```

**Response:**
```json
{
  "id": 1,
  "content": "Hello everyone! This is my first message. 👋",
  "createdAt": "2025-06-20T07:10:18.957Z",
  "updatedAt": "2025-06-20T07:10:18.957Z",
  "userId": 1,
  "user": {
    "id": 1,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "fullName": "John Doe"
  }
}
```

#### GET /api/Messages/{id}
Get a specific message by ID (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

#### PUT /api/Messages/{id}
Update a message (requires authentication, only message owner can update).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "content": "Updated message content"
}
```

#### DELETE /api/Messages/{id}
Delete a message (requires authentication, only message owner can delete).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

#### GET /api/Messages/my
Get current user's messages only (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `pageSize` (optional): Items per page (default: 50, max: 100)

## Project Structure

```
backend/
├── Controllers/          # API Controllers
│   ├── AuthController.cs # Authentication endpoints
│   └── MessagesController.cs # Messages management endpoints
├── Data/                # Database Context
│   └── ApplicationDbContext.cs
├── Models/              # Entity Models
│   ├── User.cs          # User entity
│   ├── Message.cs       # Message entity
│   └── DTOs/            # Data Transfer Objects
│       ├── AuthDTOs.cs  # Authentication DTOs
│       └── MessageDTOs.cs # Message DTOs
├── Services/            # Business Logic Services
│   ├── IJwtService.cs   # JWT service interface
│   └── JwtService.cs    # JWT token service
├── Migrations/          # EF Core Migrations
│   └── [timestamp]_InitialCreateWithAuth.cs
├── Program.cs           # Application entry point with JWT config
├── appsettings.json     # Configuration with JWT settings
├── README.md            # This documentation
└── database.db         # SQLite database file
```

## Sample API Usage

### Register a New User
```bash
curl -X POST http://localhost:5073/api/Auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
  }'
```

### Login
```bash
curl -X POST http://localhost:5073/api/Auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Get Current User (Protected Route)
```bash
curl -X GET http://localhost:5073/api/Auth/me \
  -H "Authorization: Bearer <your-jwt-token>"
```

### Update Profile
```bash
curl -X PUT http://localhost:5073/api/Auth/profile \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Smith",
    "email": "<EMAIL>"
  }'
```

### Change Password
```bash
curl -X PUT http://localhost:5073/api/Auth/change-password \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "currentPassword": "password123",
    "newPassword": "newpassword456",
    "confirmNewPassword": "newpassword456"
  }'
```

### Create a Message
```bash
curl -X POST http://localhost:5073/api/Messages \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Hello everyone! This is my first message. 👋"
  }'
```

### Get All Messages
```bash
curl -X GET http://localhost:5073/api/Messages \
  -H "Authorization: Bearer <your-jwt-token>"
```

### Get My Messages Only
```bash
curl -X GET http://localhost:5073/api/Messages/my \
  -H "Authorization: Bearer <your-jwt-token>"
```

### Update a Message
```bash
curl -X PUT http://localhost:5073/api/Messages/1 \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "Updated message content"
  }'
```

### Delete a Message
```bash
curl -X DELETE http://localhost:5073/api/Messages/1 \
  -H "Authorization: Bearer <your-jwt-token>"
```

## JWT Configuration

The JWT settings are configured in `appsettings.json`:

```json
{
  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLongForSecurity!",
    "Issuer": "StagecoachApp",
    "Audience": "StagecoachApp",
    "ExpiryMinutes": "60"
  }
}
```

**Important Security Notes:**
- Change the `SecretKey` in production to a secure, randomly generated key
- Use environment variables for sensitive configuration in production
- The secret key should be at least 32 characters long
- Consider shorter expiry times for enhanced security

## Adding Custom Controllers

You can now add custom controllers that use the authentication system:

```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize] // Require authentication for all endpoints
public class YourController : ControllerBase
{
    [HttpGet]
    public IActionResult GetData()
    {
        // Get current user ID from JWT token
        var userIdClaim = User.FindFirst("userId")?.Value;
        if (int.TryParse(userIdClaim, out int userId))
        {
            // Use userId for user-specific operations
        }

        return Ok("Your protected data");
    }

    [HttpGet("public")]
    [AllowAnonymous] // Allow anonymous access to specific endpoints
    public IActionResult GetPublicData()
    {
        return Ok("Public data");
    }
}
```

## Technologies Used

- **.NET 8** - Framework
- **ASP.NET Core Web API** - REST API framework
- **Entity Framework Core** - ORM
- **SQLite** - Database
- **Swagger/OpenAPI** - API documentation
- **System.Text.Json** - JSON serialization
