# .NET 8 Backend REST API with Entity Framework Core

This is a .NET 8 Web API backend with Entity Framework Core connected to a SQLite database file.

## Features

- ✅ **REST API** with full CRUD operations
- ✅ **Entity Framework Core** with SQLite database
- ✅ **Database Migrations** for schema management
- ✅ **CORS Configuration** for React frontend integration
- ✅ **Swagger/OpenAPI** documentation
- ✅ **JSON Serialization** with cycle handling
- ✅ **Logging** and error handling
- ✅ **Data Validation** with model annotations

## Database Schema

### Entities

**Category**
- Id (Primary Key)
- Name (Required, Max 100 chars)
- Description (Optional, Max 500 chars)
- CreatedAt, UpdatedAt (Timestamps)
- Products (Navigation property)

**Product**
- Id (Primary Key)
- Name (Required, Max 200 chars)
- Description (Optional, Max 1000 chars)
- Price (Decimal, Required)
- Stock (Integer, Required)
- SKU (Optional, Max 50 chars)
- IsActive (Boolean, default true)
- CreatedAt, UpdatedAt (Timestamps)
- CategoryId (Foreign Key)
- Category (Navigation property)

## API Endpoints

### Products
- `GET /api/products` - Get all products with categories
- `GET /api/products/{id}` - Get product by ID
- `POST /api/products` - Create new product
- `PUT /api/products/{id}` - Update product
- `DELETE /api/products/{id}` - Delete product

### Categories
- `GET /api/categories` - Get all categories with products
- `GET /api/categories/{id}` - Get category by ID
- `POST /api/categories` - Create new category
- `PUT /api/categories/{id}` - Update category
- `DELETE /api/categories/{id}` - Delete category (if no products)

## Getting Started

### Prerequisites
- .NET 8 SDK
- SQLite (included with .NET)

### Running the API

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Restore packages:**
   ```bash
   dotnet restore
   ```

3. **Run the application:**
   ```bash
   dotnet run
   ```

4. **Access the API:**
   - API Base URL: `http://localhost:5073`
   - Swagger UI: `http://localhost:5073/swagger`

### Database

The SQLite database file (`database.db`) is created automatically when the application starts. The database includes seed data with sample categories and products.

### CORS Configuration

The API is configured to accept requests from:
- `http://localhost:5173` (Vite dev server - primary)
- `http://localhost:5174` (Vite dev server - fallback)
- `http://localhost:3000` (Create React App)

## Project Structure

```
backend/
├── Controllers/           # API Controllers
│   ├── ProductsController.cs
│   └── CategoriesController.cs
├── Data/                 # Database Context
│   └── ApplicationDbContext.cs
├── Models/               # Entity Models
│   ├── Product.cs
│   └── Category.cs
├── Migrations/           # EF Core Migrations
├── Program.cs           # Application entry point
├── appsettings.json     # Configuration
└── database.db         # SQLite database file
```

## Sample API Calls

### Get All Products
```bash
curl http://localhost:5073/api/products
```

### Create New Product
```bash
curl -X POST http://localhost:5073/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New Product",
    "description": "Product description",
    "price": 99.99,
    "stock": 10,
    "sku": "NP001",
    "categoryId": 1
  }'
```

### Get All Categories
```bash
curl http://localhost:5073/api/categories
```

## Technologies Used

- **.NET 8** - Framework
- **ASP.NET Core Web API** - REST API framework
- **Entity Framework Core** - ORM
- **SQLite** - Database
- **Swagger/OpenAPI** - API documentation
- **System.Text.Json** - JSON serialization
