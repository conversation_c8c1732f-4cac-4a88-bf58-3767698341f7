# .NET 8 Backend REST API with Entity Framework Core

This is a .NET 8 Web API backend with Entity Framework Core connected to a SQLite database file. The backend is ready for custom controllers and models to be added based on specific use-case requirements.

## Features

- ✅ **REST API Framework** ready for custom controllers
- ✅ **Entity Framework Core** with SQLite database
- ✅ **Database Migrations** for schema management
- ✅ **CORS Configuration** for React frontend integration
- ✅ **Swagger/OpenAPI** documentation
- ✅ **JSON Serialization** with cycle handling
- ✅ **Logging** and error handling
- ✅ **Clean Architecture** ready for custom implementation

## Current State

The backend is currently in a clean state with:
- **No Controllers**: Ready for custom controllers to be added
- **No Models**: Ready for custom entities to be defined
- **Empty DbContext**: Ready for custom entity configurations
- **No Migrations**: Clean slate for new database schema

## Database Setup

The ApplicationDbContext is configured and ready to use:
- **SQLite Database**: Configured with connection string
- **Entity Framework Core**: Latest version with all necessary packages
- **Migration Support**: Ready to create initial migrations when models are added

## Getting Started

### Prerequisites
- .NET 8 SDK
- SQLite (included with .NET)

### Running the API

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Restore packages:**
   ```bash
   dotnet restore
   ```

3. **Run the application:**
   ```bash
   dotnet run
   ```

4. **Access the API:**
   - API Base URL: `http://localhost:5073`
   - Swagger UI: `http://localhost:5073/swagger`

### Database

The SQLite database will be created automatically when:
1. Models are added to the DbContext
2. Migrations are created and applied
3. The application runs with the new schema

### CORS Configuration

The API is configured to accept requests from:
- `http://localhost:5173` (Vite dev server - primary)
- `http://localhost:5174` (Vite dev server - fallback)
- `http://localhost:3000` (Create React App)

## Project Structure

```
backend/
├── Controllers/          # API Controllers (empty - ready for custom controllers)
├── Data/                # Database Context
│   └── ApplicationDbContext.cs
├── Models/              # Entity Models (empty - ready for custom models)
├── Migrations/          # EF Core Migrations (empty - ready for initial migration)
├── Program.cs           # Application entry point
├── appsettings.json     # Configuration with SQLite connection string
├── README.md            # This documentation
└── [database.db]       # SQLite database file (created when models are added)
```

## Adding Custom Implementation

### 1. Create Models
Add your entity models to the `Models/` directory:
```csharp
// Example: Models/YourEntity.cs
public class YourEntity
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    // Add your properties here
}
```

### 2. Update DbContext
Add your entities to the `ApplicationDbContext`:
```csharp
public DbSet<YourEntity> YourEntities { get; set; }
```

### 3. Create Migration
```bash
dotnet ef migrations add InitialCreate
```

### 4. Create Controllers
Add controllers to the `Controllers/` directory:
```csharp
[ApiController]
[Route("api/[controller]")]
public class YourEntityController : ControllerBase
{
    // Add your API endpoints here
}
```

### 5. Run Application
```bash
dotnet run
```

## Technologies Used

- **.NET 8** - Framework
- **ASP.NET Core Web API** - REST API framework
- **Entity Framework Core** - ORM
- **SQLite** - Database
- **Swagger/OpenAPI** - API documentation
- **System.Text.Json** - JSON serialization
