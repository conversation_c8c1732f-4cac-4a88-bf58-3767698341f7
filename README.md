# Full-Stack Application: React Frontend + .NET 8 Backend

A complete full-stack application with a React frontend featuring dark mode and a .NET 8 REST API backend with Entity Framework Core and SQLite database.

## 🚀 Project Overview

This project demonstrates a modern full-stack architecture:

- **Frontend**: React with TypeScript, Vite, and dark mode theme system
- **Backend**: .NET 8 Web API with Entity Framework Core and SQLite
- **Database**: SQLite with automatic migrations and seed data
- **API Documentation**: Swagger/OpenAPI integration
- **CORS**: Configured for seamless frontend-backend communication

## 📁 Project Structure

```
stagecoach-augment/
├── frontend/                    # React Frontend
│   ├── src/                     # React Source Code
│   │   ├── components/          # React Components
│   │   │   ├── Header.tsx       # Header with theme toggle
│   │   │   └── ThemeToggle.tsx  # Dark/Light mode toggle
│   │   ├── contexts/            # React Contexts
│   │   │   └── ThemeContext.tsx # Theme management
│   │   ├── App.tsx              # Main App component
│   │   └── main.tsx             # App entry point
│   ├── public/                  # Static assets
│   ├── package.json             # Frontend dependencies
│   └── README.md                # Frontend documentation
├── backend/                     # .NET 8 Backend
│   ├── Controllers/             # API Controllers
│   │   ├── ProductsController.cs
│   │   └── CategoriesController.cs
│   ├── Data/                    # Database Context
│   │   └── ApplicationDbContext.cs
│   ├── Models/                  # Entity Models
│   │   ├── Product.cs
│   │   └── Category.cs
│   ├── Migrations/              # EF Core Migrations
│   ├── Program.cs               # API entry point
│   ├── database.db              # SQLite database file
│   └── README.md                # Backend documentation
└── README.md                    # This file (main documentation)
```

## 🎨 Frontend Features

- **Dark Mode Theme System**: Toggle between light and dark themes
- **Theme Persistence**: Automatically saves and restores theme preference
- **System Preference Detection**: Detects user's system color scheme
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern React**: Uses hooks, context, and TypeScript
- **Hot Module Replacement**: Fast development with Vite

## 🔧 Backend Features

- **JWT Authentication**: Complete user registration and login system
- **Secure Password Hashing**: BCrypt for password security
- **Protected Endpoints**: Authorization middleware with Bearer tokens
- **User Profile Management**: Update profile and change password
- **Entity Framework Core**: Modern ORM with SQLite database
- **Database Migrations**: Automatic schema management
- **CORS Configuration**: Ready for frontend integration
- **Swagger Documentation**: Interactive API documentation with auth
- **Comprehensive Logging**: Error handling and request logging
- **Token Validation**: JWT token generation and validation service

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher)
- **.NET 8 SDK**
- **Git**

### 1. Clone and Setup

```bash
git clone <repository-url>
cd stagecoach-augment
```

### 2. Start the Backend (.NET API)

```bash
cd backend
dotnet restore
dotnet run
```

The API will be available at:
- **API**: `http://localhost:5073`
- **Swagger UI**: `http://localhost:5073/swagger`

### 3. Start the Frontend (React)

```bash
# In a new terminal, navigate to frontend directory
cd frontend
npm install
npm run dev
```

The React app will be available at:
- **Frontend**: `http://localhost:5173` (or next available port)

## 📊 API Endpoints

### Authentication Endpoints
- `POST /api/Auth/register` - Register new user account
- `POST /api/Auth/login` - User login with JWT token response
- `GET /api/Auth/me` - Get current user profile (protected)
- `PUT /api/Auth/profile` - Update user profile (protected)
- `PUT /api/Auth/change-password` - Change user password (protected)

### Messages Endpoints
- `GET /api/Messages` - Get all messages from all users (protected)
- `POST /api/Messages` - Create a new message (protected)
- `GET /api/Messages/{id}` - Get specific message by ID (protected)
- `PUT /api/Messages/{id}` - Update message (protected, owner only)
- `DELETE /api/Messages/{id}` - Delete message (protected, owner only)
- `GET /api/Messages/my` - Get current user's messages only (protected)

**API Status:**
- ✅ **JWT Authentication**: Complete with Bearer token support
- ✅ **User Registration**: Secure account creation with validation
- ✅ **User Login**: Authentication with token generation
- ✅ **Messages System**: Full CRUD operations for user messages
- ✅ **Message Ordering**: Messages displayed by creation time (newest first)
- ✅ **User Ownership**: Users can only edit/delete their own messages
- ✅ **Pagination**: Support for paginated message retrieval
- ✅ **Protected Routes**: Authorization middleware implemented
- ✅ **Swagger UI**: Available at `http://localhost:5073/swagger`
- ✅ **Password Security**: BCrypt hashing for secure storage

## 🗄️ Database Schema

### User Entity (Authentication)
- **Id**: Primary key (auto-increment)
- **FirstName**: User's first name (required, max 100 chars)
- **LastName**: User's last name (required, max 100 chars)
- **Email**: User's email address (required, unique, max 255 chars)
- **PasswordHash**: BCrypt hashed password (required)
- **IsActive**: Account status (boolean, default true)
- **CreatedAt**: Account creation timestamp
- **UpdatedAt**: Last update timestamp
- **LastLoginAt**: Last login timestamp (nullable)

### Message Entity
- **Id**: Primary key (auto-increment)
- **Content**: Message content (required, max 1000 chars)
- **CreatedAt**: Message creation timestamp
- **UpdatedAt**: Last update timestamp
- **UserId**: Foreign key to User (required)
- **User**: Navigation property to User entity

**Database Status:**
- ✅ **SQLite Database**: Automatically created with Users table
- ✅ **Entity Framework Core**: Configured with migrations
- ✅ **User Authentication**: Complete schema implemented
- ✅ **Unique Constraints**: Email uniqueness enforced
- ✅ **Automatic Timestamps**: CreatedAt and UpdatedAt defaults
- ✅ **Migration Applied**: Database ready for use

**Ready for Extension:**
- Add custom entity models to `backend/Models/`
- Configure entities in `ApplicationDbContext`
- Create additional migrations as needed

## 🛠️ Technologies Used

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development
- **CSS Variables** for theming
- **Context API** for state management

### Backend
- **.NET 8** Web API
- **Entity Framework Core** ORM
- **SQLite** database
- **Swagger/OpenAPI** documentation
- **System.Text.Json** serialization

## 🎯 Key Features Demonstrated

1. **Full-Stack Integration**: Seamless communication between React and .NET
2. **Modern Development**: Latest versions of React and .NET
3. **Database Management**: EF Core migrations and seed data
4. **API Documentation**: Auto-generated Swagger documentation
5. **Theme System**: Complete dark/light mode implementation
6. **Error Handling**: Comprehensive error handling on both ends
7. **CORS Configuration**: Proper cross-origin setup
8. **TypeScript**: Full type safety in the frontend

## 📝 Sample API Usage

### Register a New User
```bash
curl -X POST http://localhost:5073/api/Auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
  }'
```

### Login and Get JWT Token
```bash
curl -X POST http://localhost:5073/api/Auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Access Protected Endpoint
```bash
curl -X GET http://localhost:5073/api/Auth/me \
  -H "Authorization: Bearer <your-jwt-token>"
```

### Access Swagger Documentation
Open `http://localhost:5073/swagger` in your browser to explore all endpoints interactively.

**Authentication System Ready:**
- ✅ Complete user registration and login
- ✅ JWT token-based authentication
- ✅ Protected endpoints with authorization
- ✅ User profile management
- ✅ Password change functionality

## 🔄 Development Workflow

1. **Backend Changes**: Modify controllers, models, or add migrations
2. **Frontend Changes**: Update components, add features, or modify themes
3. **Database Changes**: Create migrations with `dotnet ef migrations add`
4. **Testing**: Use Swagger UI or curl to test API endpoints
5. **Integration**: Test frontend-backend communication

This project serves as a solid foundation for building modern full-stack applications with React and .NET!