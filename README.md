# Full-Stack Application: React Frontend + .NET 8 Backend

A complete full-stack application with a React frontend featuring dark mode and a .NET 8 REST API backend with Entity Framework Core and SQLite database.

## 🚀 Project Overview

This project demonstrates a modern full-stack architecture:

- **Frontend**: React with TypeScript, Vite, and dark mode theme system
- **Backend**: .NET 8 Web API with Entity Framework Core and SQLite
- **Database**: SQLite with automatic migrations and seed data
- **API Documentation**: Swagger/OpenAPI integration
- **CORS**: Configured for seamless frontend-backend communication

## 📁 Project Structure

```
stagecoach-augment/
├── frontend/                    # React Frontend
│   ├── src/                     # React Source Code
│   │   ├── components/          # React Components
│   │   │   ├── Header.tsx       # Header with theme toggle
│   │   │   └── ThemeToggle.tsx  # Dark/Light mode toggle
│   │   ├── contexts/            # React Contexts
│   │   │   └── ThemeContext.tsx # Theme management
│   │   ├── App.tsx              # Main App component
│   │   └── main.tsx             # App entry point
│   ├── public/                  # Static assets
│   ├── package.json             # Frontend dependencies
│   └── README.md                # Frontend documentation
├── backend/                     # .NET 8 Backend
│   ├── Controllers/             # API Controllers
│   │   ├── ProductsController.cs
│   │   └── CategoriesController.cs
│   ├── Data/                    # Database Context
│   │   └── ApplicationDbContext.cs
│   ├── Models/                  # Entity Models
│   │   ├── Product.cs
│   │   └── Category.cs
│   ├── Migrations/              # EF Core Migrations
│   ├── Program.cs               # API entry point
│   ├── database.db              # SQLite database file
│   └── README.md                # Backend documentation
└── README.md                    # This file (main documentation)
```

## 🎨 Frontend Features

- **Dark Mode Theme System**: Toggle between light and dark themes
- **Theme Persistence**: Automatically saves and restores theme preference
- **System Preference Detection**: Detects user's system color scheme
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Modern React**: Uses hooks, context, and TypeScript
- **Hot Module Replacement**: Fast development with Vite

## 🔧 Backend Features

- **REST API**: Full CRUD operations for Products and Categories
- **Entity Framework Core**: Modern ORM with SQLite
- **Database Migrations**: Automatic schema management
- **Seed Data**: Pre-populated sample data
- **CORS Configuration**: Ready for frontend integration
- **Swagger Documentation**: Interactive API documentation
- **Error Handling**: Comprehensive error handling and logging
- **Data Validation**: Model validation with annotations

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher)
- **.NET 8 SDK**
- **Git**

### 1. Clone and Setup

```bash
git clone <repository-url>
cd stagecoach-augment
```

### 2. Start the Backend (.NET API)

```bash
cd backend
dotnet restore
dotnet run
```

The API will be available at:
- **API**: `http://localhost:5073`
- **Swagger UI**: `http://localhost:5073/swagger`

### 3. Start the Frontend (React)

```bash
# In a new terminal, navigate to frontend directory
cd frontend
npm install
npm run dev
```

The React app will be available at:
- **Frontend**: `http://localhost:5173` (or next available port)

## 📊 API Endpoints

### Products
- `GET /api/products` - Get all products
- `GET /api/products/{id}` - Get product by ID
- `POST /api/products` - Create new product
- `PUT /api/products/{id}` - Update product
- `DELETE /api/products/{id}` - Delete product

### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/{id}` - Get category by ID
- `POST /api/categories` - Create new category
- `PUT /api/categories/{id}` - Update category
- `DELETE /api/categories/{id}` - Delete category

## 🗄️ Database Schema

### Product Entity
- **Id**: Primary key
- **Name**: Product name (required)
- **Description**: Product description
- **Price**: Product price (decimal)
- **Stock**: Available quantity
- **SKU**: Stock keeping unit
- **IsActive**: Active status
- **CategoryId**: Foreign key to Category
- **CreatedAt/UpdatedAt**: Timestamps

### Category Entity
- **Id**: Primary key
- **Name**: Category name (required)
- **Description**: Category description
- **CreatedAt/UpdatedAt**: Timestamps

## 🛠️ Technologies Used

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development
- **CSS Variables** for theming
- **Context API** for state management

### Backend
- **.NET 8** Web API
- **Entity Framework Core** ORM
- **SQLite** database
- **Swagger/OpenAPI** documentation
- **System.Text.Json** serialization

## 🎯 Key Features Demonstrated

1. **Full-Stack Integration**: Seamless communication between React and .NET
2. **Modern Development**: Latest versions of React and .NET
3. **Database Management**: EF Core migrations and seed data
4. **API Documentation**: Auto-generated Swagger documentation
5. **Theme System**: Complete dark/light mode implementation
6. **Error Handling**: Comprehensive error handling on both ends
7. **CORS Configuration**: Proper cross-origin setup
8. **TypeScript**: Full type safety in the frontend

## 📝 Sample API Usage

```bash
# Get all products
curl http://localhost:5073/api/products

# Create a new product
curl -X POST http://localhost:5073/api/products \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New Product",
    "description": "Product description",
    "price": 99.99,
    "stock": 10,
    "sku": "NP001",
    "categoryId": 1
  }'
```

## 🔄 Development Workflow

1. **Backend Changes**: Modify controllers, models, or add migrations
2. **Frontend Changes**: Update components, add features, or modify themes
3. **Database Changes**: Create migrations with `dotnet ef migrations add`
4. **Testing**: Use Swagger UI or curl to test API endpoints
5. **Integration**: Test frontend-backend communication

This project serves as a solid foundation for building modern full-stack applications with React and .NET!