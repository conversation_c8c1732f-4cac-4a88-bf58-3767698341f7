using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using backend.Data;
using backend.Models;
using backend.Models.DTOs;
using System.Security.Claims;

namespace backend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MessagesController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<MessagesController> _logger;

        public MessagesController(ApplicationDbContext context, ILogger<MessagesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        // GET: api/Messages
        [HttpGet]
        public async Task<ActionResult<MessagesResponseDto>> GetMessages(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                // Validate pagination parameters
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 50;

                var totalCount = await _context.Messages.CountAsync();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var messages = await _context.Messages
                    .Include(m => m.User)
                    .OrderByDescending(m => m.CreatedAt) // Order by time added (newest first)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(m => new MessageDto
                    {
                        Id = m.Id,
                        Content = m.Content,
                        CreatedAt = m.CreatedAt,
                        UpdatedAt = m.UpdatedAt,
                        UserId = m.UserId,
                        User = new MessageUserDto
                        {
                            Id = m.User.Id,
                            FirstName = m.User.FirstName,
                            LastName = m.User.LastName,
                            Email = m.User.Email
                        }
                    })
                    .ToListAsync();

                var response = new MessagesResponseDto
                {
                    Messages = messages,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = totalPages
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving messages");
                return StatusCode(500, "Internal server error while retrieving messages");
            }
        }

        // GET: api/Messages/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<MessageDto>> GetMessage(int id)
        {
            try
            {
                var message = await _context.Messages
                    .Include(m => m.User)
                    .FirstOrDefaultAsync(m => m.Id == id);

                if (message == null)
                {
                    return NotFound("Message not found");
                }

                var messageDto = new MessageDto
                {
                    Id = message.Id,
                    Content = message.Content,
                    CreatedAt = message.CreatedAt,
                    UpdatedAt = message.UpdatedAt,
                    UserId = message.UserId,
                    User = new MessageUserDto
                    {
                        Id = message.User.Id,
                        FirstName = message.User.FirstName,
                        LastName = message.User.LastName,
                        Email = message.User.Email
                    }
                };

                return Ok(messageDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving message {MessageId}", id);
                return StatusCode(500, "Internal server error while retrieving message");
            }
        }

        // POST: api/Messages
        [HttpPost]
        public async Task<ActionResult<MessageDto>> CreateMessage(CreateMessageDto createMessageDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get current user ID from JWT token
                var userIdClaim = User.FindFirst("userId")?.Value;
                if (userIdClaim == null || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized("Invalid token");
                }

                // Verify user exists and is active
                var user = await _context.Users.FindAsync(userId);
                if (user == null || !user.IsActive)
                {
                    return Unauthorized("User not found or inactive");
                }

                // Create new message
                var message = new Message
                {
                    Content = createMessageDto.Content.Trim(),
                    UserId = userId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Messages.Add(message);
                await _context.SaveChangesAsync();

                // Load the user information for the response
                await _context.Entry(message)
                    .Reference(m => m.User)
                    .LoadAsync();

                var messageDto = new MessageDto
                {
                    Id = message.Id,
                    Content = message.Content,
                    CreatedAt = message.CreatedAt,
                    UpdatedAt = message.UpdatedAt,
                    UserId = message.UserId,
                    User = new MessageUserDto
                    {
                        Id = message.User.Id,
                        FirstName = message.User.FirstName,
                        LastName = message.User.LastName,
                        Email = message.User.Email
                    }
                };

                _logger.LogInformation("Message created successfully by user {UserId}", userId);
                return CreatedAtAction(nameof(GetMessage), new { id = message.Id }, messageDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating message");
                return StatusCode(500, "Internal server error while creating message");
            }
        }

        // PUT: api/Messages/{id}
        [HttpPut("{id}")]
        public async Task<ActionResult<MessageDto>> UpdateMessage(int id, UpdateMessageDto updateMessageDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get current user ID from JWT token
                var userIdClaim = User.FindFirst("userId")?.Value;
                if (userIdClaim == null || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized("Invalid token");
                }

                var message = await _context.Messages
                    .Include(m => m.User)
                    .FirstOrDefaultAsync(m => m.Id == id);

                if (message == null)
                {
                    return NotFound("Message not found");
                }

                // Check if the current user is the owner of the message
                if (message.UserId != userId)
                {
                    return Forbid("You can only update your own messages");
                }

                // Update message
                message.Content = updateMessageDto.Content.Trim();
                message.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                var messageDto = new MessageDto
                {
                    Id = message.Id,
                    Content = message.Content,
                    CreatedAt = message.CreatedAt,
                    UpdatedAt = message.UpdatedAt,
                    UserId = message.UserId,
                    User = new MessageUserDto
                    {
                        Id = message.User.Id,
                        FirstName = message.User.FirstName,
                        LastName = message.User.LastName,
                        Email = message.User.Email
                    }
                };

                _logger.LogInformation("Message {MessageId} updated successfully by user {UserId}", id, userId);
                return Ok(messageDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating message {MessageId}", id);
                return StatusCode(500, "Internal server error while updating message");
            }
        }

        // DELETE: api/Messages/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteMessage(int id)
        {
            try
            {
                // Get current user ID from JWT token
                var userIdClaim = User.FindFirst("userId")?.Value;
                if (userIdClaim == null || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized("Invalid token");
                }

                var message = await _context.Messages.FindAsync(id);
                if (message == null)
                {
                    return NotFound("Message not found");
                }

                // Check if the current user is the owner of the message
                if (message.UserId != userId)
                {
                    return Forbid("You can only delete your own messages");
                }

                _context.Messages.Remove(message);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Message {MessageId} deleted successfully by user {UserId}", id, userId);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting message {MessageId}", id);
                return StatusCode(500, "Internal server error while deleting message");
            }
        }

        // GET: api/Messages/my
        [HttpGet("my")]
        public async Task<ActionResult<MessagesResponseDto>> GetMyMessages(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50)
        {
            try
            {
                // Get current user ID from JWT token
                var userIdClaim = User.FindFirst("userId")?.Value;
                if (userIdClaim == null || !int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized("Invalid token");
                }

                // Validate pagination parameters
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 50;

                var totalCount = await _context.Messages
                    .Where(m => m.UserId == userId)
                    .CountAsync();

                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var messages = await _context.Messages
                    .Include(m => m.User)
                    .Where(m => m.UserId == userId)
                    .OrderByDescending(m => m.CreatedAt)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(m => new MessageDto
                    {
                        Id = m.Id,
                        Content = m.Content,
                        CreatedAt = m.CreatedAt,
                        UpdatedAt = m.UpdatedAt,
                        UserId = m.UserId,
                        User = new MessageUserDto
                        {
                            Id = m.User.Id,
                            FirstName = m.User.FirstName,
                            LastName = m.User.LastName,
                            Email = m.User.Email
                        }
                    })
                    .ToListAsync();

                var response = new MessagesResponseDto
                {
                    Messages = messages,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize,
                    TotalPages = totalPages
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user messages for user {UserId}", User.FindFirst("userId")?.Value);
                return StatusCode(500, "Internal server error while retrieving user messages");
            }
        }
    }
}
