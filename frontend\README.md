# React Frontend with Dark Mode

A modern React frontend application built with TypeScript, Vite, and a complete dark mode theme system.

## 🎨 Features

- **Dark Mode Theme System**: Toggle between light and dark themes with smooth transitions
- **Theme Persistence**: Automatically saves and restores user theme preference in localStorage
- **System Preference Detection**: Detects and respects user's system color scheme preference
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Modern React**: Built with React 18, TypeScript, and modern hooks
- **Fast Development**: Powered by Vite for lightning-fast hot module replacement
- **CSS Variables**: Clean theming system using CSS custom properties
- **Context API**: Efficient state management for theme switching

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher)
- **npm** or **yarn**

### Installation and Setup

1. **Navigate to frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

4. **Open in browser:**
   - The app will be available at `http://localhost:5173` (or next available port)
   - Hot module replacement is enabled for instant updates

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 📁 Project Structure

```
frontend/
├── src/
│   ├── components/           # React Components
│   │   ├── Header.tsx       # Header with theme toggle
│   │   ├── Header.css       # Header styles
│   │   ├── ThemeToggle.tsx  # Dark/Light mode toggle button
│   │   └── ThemeToggle.css  # Toggle button styles
│   ├── contexts/            # React Contexts
│   │   └── ThemeContext.tsx # Theme management context
│   ├── assets/              # Static assets
│   │   └── react.svg        # React logo
│   ├── App.tsx              # Main App component
│   ├── App.css              # App-specific styles
│   ├── index.css            # Global styles and CSS variables
│   ├── main.tsx             # Application entry point
│   └── vite-env.d.ts        # Vite type definitions
├── public/
│   └── vite.svg             # Vite logo
├── index.html               # HTML template
├── package.json             # Dependencies and scripts
├── tsconfig.json            # TypeScript configuration
├── vite.config.ts           # Vite configuration
└── README.md                # This file
```

## 🎯 Key Components

### ThemeContext
- Manages global theme state (light/dark)
- Handles localStorage persistence
- Detects system color scheme preference
- Provides theme toggle functionality

### ThemeToggle
- Interactive button for switching themes
- Visual feedback with icons (🌙/☀️)
- Smooth animations and hover effects
- Accessible with proper ARIA labels

### Header
- Navigation header with app title
- Integrated theme toggle button
- Responsive design
- Sticky positioning

## 🎨 Theme System

The application uses a sophisticated theming system based on CSS variables:

### CSS Variables (Light Theme)
```css
--bg-color: #ffffff
--text-color: #213547
--accent-color: #646cff
--button-bg: #f9f9f9
--card-bg: #ffffff
```

### CSS Variables (Dark Theme)
```css
--bg-color: #242424
--text-color: rgba(255, 255, 255, 0.87)
--accent-color: #646cff
--button-bg: #1a1a1a
--card-bg: #2a2a2a
```

### Theme Features
- **Automatic Detection**: Respects `prefers-color-scheme` media query
- **Persistence**: Saves preference in localStorage
- **Smooth Transitions**: 0.3s ease transitions for all theme changes
- **System Integration**: Applies theme to document root element

## 🛠️ Technologies Used

- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type safety and better developer experience
- **Vite** - Fast build tool and development server
- **CSS Variables** - Dynamic theming system
- **Context API** - State management for theme
- **ESLint** - Code linting and formatting

## 🔧 Configuration

### Vite Configuration
The project uses Vite with React plugin for fast development and optimized builds.

### TypeScript Configuration
Strict TypeScript configuration for better code quality and type safety.

### ESLint Configuration
Modern ESLint setup with React and TypeScript rules.

## 🌐 Integration with Backend

This frontend is designed to work with the .NET 8 backend API:

- **API Base URL**: `http://localhost:5073`
- **CORS**: Backend is configured to accept requests from this frontend
- **Ready for Integration**: Can easily connect to REST API endpoints

## 📱 Responsive Design

The application is fully responsive and works on:
- **Desktop**: Full-featured experience
- **Tablet**: Optimized layout and touch interactions
- **Mobile**: Mobile-first responsive design

## 🎯 Future Enhancements

Potential improvements and features:
- API integration with backend
- User authentication
- Data fetching and state management
- Additional theme options
- Internationalization (i18n)
- Progressive Web App (PWA) features

This frontend provides a solid foundation for building modern React applications with excellent user experience and developer productivity.
