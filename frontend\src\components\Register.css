.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: var(--bg-color);
}

.register-card {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
}

.register-card h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
  font-size: 0.875rem;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.875rem;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  font-size: 1rem;
  background: var(--input-bg, var(--bg-color));
  color: var(--text-color);
  transition: border-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.1);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.field-error {
  color: #c33;
  font-size: 0.75rem;
  margin-top: -0.25rem;
}

.register-button {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
}

.register-button:hover:not(:disabled) {
  background: var(--accent-hover, #5a5fcf);
  transform: translateY(-1px);
}

.register-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
}

.auth-switch p {
  margin: 0;
  color: var(--text-color);
  font-size: 0.875rem;
}

.switch-button {
  background: none;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: underline;
  padding: 0;
  margin-left: 0.25rem;
}

.switch-button:hover:not(:disabled) {
  color: var(--accent-hover, #5a5fcf);
}

.switch-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Dark mode specific styles */
[data-theme="dark"] .register-card {
  --card-bg: #2a2a2a;
  --border-color: rgba(255, 255, 255, 0.1);
  --input-bg: #1a1a1a;
}

/* Light mode specific styles */
[data-theme="light"] .register-card {
  --card-bg: #ffffff;
  --border-color: #ddd;
  --input-bg: #ffffff;
}

/* Responsive design */
@media (max-width: 600px) {
  .register-container {
    padding: 10px;
  }
  
  .register-card {
    padding: 1.5rem;
    max-width: 100%;
  }
  
  .register-card h2 {
    font-size: 1.25rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}
