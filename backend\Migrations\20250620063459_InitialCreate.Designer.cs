﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using backend.Data;

#nullable disable

namespace backend.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250620063459_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.6");

            modelBuilder.Entity("backend.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.HasKey("Id");

                    b.ToTable("Categories");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CreatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(7118),
                            Description = "Electronic devices and gadgets",
                            Name = "Electronics",
                            UpdatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(7638)
                        },
                        new
                        {
                            Id = 2,
                            CreatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(8142),
                            Description = "Books and literature",
                            Name = "Books",
                            UpdatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(8143)
                        },
                        new
                        {
                            Id = 3,
                            CreatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(8147),
                            Description = "Apparel and accessories",
                            Name = "Clothing",
                            UpdatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(8148)
                        });
                });

            modelBuilder.Entity("backend.Models.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<int>("CategoryId")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("SKU")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<int>("Stock")
                        .HasColumnType("INTEGER");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT")
                        .HasDefaultValueSql("datetime('now')");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("Products");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CategoryId = 1,
                            CreatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(4934),
                            Description = "High-performance laptop",
                            IsActive = true,
                            Name = "Laptop",
                            Price = 999.99m,
                            SKU = "LAP001",
                            Stock = 10,
                            UpdatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5293)
                        },
                        new
                        {
                            Id = 2,
                            CategoryId = 1,
                            CreatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5820),
                            Description = "Latest smartphone",
                            IsActive = true,
                            Name = "Smartphone",
                            Price = 699.99m,
                            SKU = "PHN001",
                            Stock = 25,
                            UpdatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5820)
                        },
                        new
                        {
                            Id = 3,
                            CategoryId = 2,
                            CreatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5824),
                            Description = "Learn programming fundamentals",
                            IsActive = true,
                            Name = "Programming Book",
                            Price = 49.99m,
                            SKU = "BK001",
                            Stock = 50,
                            UpdatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5824)
                        },
                        new
                        {
                            Id = 4,
                            CategoryId = 3,
                            CreatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5827),
                            Description = "Comfortable cotton t-shirt",
                            IsActive = true,
                            Name = "T-Shirt",
                            Price = 19.99m,
                            SKU = "TSH001",
                            Stock = 100,
                            UpdatedAt = new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5828)
                        });
                });

            modelBuilder.Entity("backend.Models.Product", b =>
                {
                    b.HasOne("backend.Models.Category", "Category")
                        .WithMany("Products")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("backend.Models.Category", b =>
                {
                    b.Navigation("Products");
                });
#pragma warning restore 612, 618
        }
    }
}
