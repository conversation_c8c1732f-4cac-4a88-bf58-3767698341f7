import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import { Header } from './components/Header'
import { useTheme } from './contexts/ThemeContext'
import './App.css'

function App() {
  const [count, setCount] = useState(0)
  const { theme } = useTheme()

  return (
    <>
      <Header />
      <main className="main-content">
        <div>
          <a href="https://vite.dev" target="_blank">
            <img src={viteLogo} className="logo" alt="Vite logo" />
          </a>
          <a href="https://react.dev" target="_blank">
            <img src={reactLogo} className="logo react" alt="React logo" />
          </a>
        </div>
        <h1>Vite + React with Dark Mode</h1>
        <p>Current theme: <strong>{theme}</strong></p>

        <div className="card">
          <button onClick={() => setCount((count) => count + 1)}>
            count is {count}
          </button>
          <p>
            Edit <code>src/App.tsx</code> and save to test HMR
          </p>
        </div>

        <div className="feature-grid">
          <div className="feature-card">
            <h3>🌙 Dark Mode</h3>
            <p>Toggle between light and dark themes with smooth transitions. Your preference is saved automatically.</p>
          </div>
          <div className="feature-card">
            <h3>⚡ Fast Refresh</h3>
            <p>Built with Vite for lightning-fast development experience with hot module replacement.</p>
          </div>
          <div className="feature-card">
            <h3>🎨 Modern Design</h3>
            <p>Clean, responsive design with CSS variables and smooth animations.</p>
          </div>
          <div className="feature-card">
            <h3>📱 Responsive</h3>
            <p>Works perfectly on desktop, tablet, and mobile devices.</p>
          </div>
        </div>

        <p className="read-the-docs">
          Click on the Vite and React logos to learn more
        </p>
      </main>
    </>
  )
}

export default App
