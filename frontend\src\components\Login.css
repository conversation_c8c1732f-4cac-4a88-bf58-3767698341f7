.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: var(--bg-color);
}

.login-card {
  background: var(--card-bg);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
}

.login-card h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  font-size: 1.5rem;
  font-weight: 600;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
  font-size: 0.875rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: var(--text-color);
  font-weight: 500;
  font-size: 0.875rem;
}

.form-group input {
  padding: 0.75rem;
  border: 1px solid var(--border-color, #ddd);
  border-radius: 6px;
  font-size: 1rem;
  background: var(--input-bg, var(--bg-color));
  color: var(--text-color);
  transition: border-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(100, 108, 255, 0.1);
}

.form-group input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.login-button {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
}

.login-button:hover:not(:disabled) {
  background: var(--accent-hover, #5a5fcf);
  transform: translateY(-1px);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.demo-section {
  margin: 1.5rem 0;
  padding: 1rem;
  background: var(--demo-bg, rgba(100, 108, 255, 0.05));
  border-radius: 6px;
  border: 1px solid var(--demo-border, rgba(100, 108, 255, 0.1));
  text-align: center;
}

.demo-section p {
  margin: 0 0 0.75rem 0;
  color: var(--text-color);
  font-size: 0.875rem;
}

.demo-button {
  background: var(--demo-button-bg, #f0f0f0);
  color: var(--demo-button-color, #333);
  border: 1px solid var(--demo-button-border, #ddd);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 0.75rem;
}

.demo-button:hover:not(:disabled) {
  background: var(--demo-button-hover, #e0e0e0);
}

.demo-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.demo-info {
  font-size: 0.75rem;
  color: var(--text-secondary, #666);
  margin: 0;
  line-height: 1.4;
}

.auth-switch {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
}

.auth-switch p {
  margin: 0;
  color: var(--text-color);
  font-size: 0.875rem;
}

.switch-button {
  background: none;
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: underline;
  padding: 0;
  margin-left: 0.25rem;
}

.switch-button:hover:not(:disabled) {
  color: var(--accent-hover, #5a5fcf);
}

.switch-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Dark mode specific styles */
[data-theme="dark"] .login-card {
  --card-bg: #2a2a2a;
  --border-color: rgba(255, 255, 255, 0.1);
  --input-bg: #1a1a1a;
  --demo-bg: rgba(100, 108, 255, 0.1);
  --demo-border: rgba(100, 108, 255, 0.2);
  --demo-button-bg: #3a3a3a;
  --demo-button-color: #fff;
  --demo-button-border: #555;
  --demo-button-hover: #4a4a4a;
  --text-secondary: #aaa;
}

/* Light mode specific styles */
[data-theme="light"] .login-card {
  --card-bg: #ffffff;
  --border-color: #ddd;
  --input-bg: #ffffff;
  --demo-bg: rgba(100, 108, 255, 0.05);
  --demo-border: rgba(100, 108, 255, 0.1);
  --demo-button-bg: #f0f0f0;
  --demo-button-color: #333;
  --demo-button-border: #ddd;
  --demo-button-hover: #e0e0e0;
  --text-secondary: #666;
}

/* Responsive design */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }
  
  .login-card {
    padding: 1.5rem;
  }
  
  .login-card h2 {
    font-size: 1.25rem;
  }
}
