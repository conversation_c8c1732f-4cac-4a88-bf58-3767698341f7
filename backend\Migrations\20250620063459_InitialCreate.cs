﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Products",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    Stock = table.Column<int>(type: "INTEGER", nullable: false),
                    SKU = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: false, defaultValueSql: "datetime('now')"),
                    CategoryId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Products", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Products_Categories_CategoryId",
                        column: x => x.CategoryId,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.InsertData(
                table: "Categories",
                columns: new[] { "Id", "CreatedAt", "Description", "Name", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(7118), "Electronic devices and gadgets", "Electronics", new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(7638) },
                    { 2, new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(8142), "Books and literature", "Books", new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(8143) },
                    { 3, new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(8147), "Apparel and accessories", "Clothing", new DateTime(2025, 6, 20, 6, 34, 58, 340, DateTimeKind.Utc).AddTicks(8148) }
                });

            migrationBuilder.InsertData(
                table: "Products",
                columns: new[] { "Id", "CategoryId", "CreatedAt", "Description", "IsActive", "Name", "Price", "SKU", "Stock", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, 1, new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(4934), "High-performance laptop", true, "Laptop", 999.99m, "LAP001", 10, new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5293) },
                    { 2, 1, new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5820), "Latest smartphone", true, "Smartphone", 699.99m, "PHN001", 25, new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5820) },
                    { 3, 2, new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5824), "Learn programming fundamentals", true, "Programming Book", 49.99m, "BK001", 50, new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5824) },
                    { 4, 3, new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5827), "Comfortable cotton t-shirt", true, "T-Shirt", 19.99m, "TSH001", 100, new DateTime(2025, 6, 20, 6, 34, 58, 342, DateTimeKind.Utc).AddTicks(5828) }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Products_CategoryId",
                table: "Products",
                column: "CategoryId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Products");

            migrationBuilder.DropTable(
                name: "Categories");
        }
    }
}
